import type { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Admin Dashboard | Denis Erast<PERSON>',
  description: 'Admin interface for managing bookings and portfolio content',
  robots: 'noindex, nofollow', // Prevent search engine indexing
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="admin-layout">
      <nav className="admin-nav">
        <div className="nav-brand">
          <h1>Admin Dashboard</h1>
        </div>
        <div className="nav-links">
          <a href="/admin/bookings" className="nav-link">
            📅 Bookings
          </a>
          <a href="/" className="nav-link">
            🏠 Back to Site
          </a>
        </div>
      </nav>
      <main className="admin-main">
        {children}
      </main>
      
      <style jsx>{`
        .admin-layout {
          min-height: 100vh;
          background: var(--bg-primary);
        }
        
        .admin-nav {
          background: var(--glass-bg);
          border-bottom: 1px solid var(--glass-border);
          backdrop-filter: blur(20px);
          padding: 16px 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .nav-brand h1 {
          color: #ffffff;
          font-size: 1.5rem;
          font-weight: 600;
          margin: 0;
        }
        
        .nav-links {
          display: flex;
          gap: 20px;
        }
        
        .nav-link {
          color: rgba(255, 255, 255, 0.8);
          text-decoration: none;
          padding: 8px 16px;
          border-radius: 8px;
          transition: all 0.3s ease;
          font-weight: 500;
        }
        
        .nav-link:hover {
          color: #ffffff;
          background: rgba(255, 255, 255, 0.1);
        }
        
        .admin-main {
          flex: 1;
        }
        
        @media (max-width: 768px) {
          .admin-nav {
            flex-direction: column;
            gap: 16px;
            align-items: flex-start;
          }
          
          .nav-links {
            width: 100%;
            justify-content: flex-start;
          }
        }
      `}</style>
    </div>
  )
}
