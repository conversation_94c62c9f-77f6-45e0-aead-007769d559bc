# Denis Erastus Portfolio

> AI Automation Expert Portfolio - Modern Next.js application with Supabase integration

[![CI/CD Pipeline](https://github.com/ProDevDenis/MyPortfolio/actions/workflows/ci.yml/badge.svg)](https://github.com/ProDevDenis/MyPortfolio/actions/workflows/ci.yml)
[![Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black)](https://vercel.com)
[![Supabase](https://img.shields.io/badge/Database-Supabase-green)](https://supabase.com)

## 🚀 Live Demo

- **Production**: [my-portfolio-git-main-devdenis-projects.vercel.app](https://my-portfolio-git-main-devdenis-projects.vercel.app/) ✅
- **Staging**: [my-portfolio-git-develop-devdenis-projects.vercel.app](https://my-portfolio-git-develop-devdenis-projects.vercel.app/) ✅

> **Status**: Both environments are live and fully functional with error-free deployments!

## 🎯 Project Overview

Professional portfolio website for Denis Erastus, showcasing AI automation expertise and services. Built with modern web technologies and best practices.

### ✨ Features

- **Responsive Design** - Optimized for all devices with separate mobile/desktop navigation
- **Modern Navigation** - Glassmorphism mobile modal with proper positioning for all screen sizes
- **AI Automation Focus** - Showcases expertise and case studies
- **Contact Management** - Integrated contact forms with database storage
- **Analytics Tracking** - Custom analytics implementation
- **SEO Optimized** - Server-side rendering for better search visibility
- **Performance Focused** - Sub-2 second loading times

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Custom CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Deployment**: Vercel
- **CI/CD**: GitHub Actions

## 📋 Quick Start

### Prerequisites

- Node.js 18+
- Git
- GitHub account
- Vercel account
- Supabase account

### Installation

```bash
# Clone the repository
git clone https://github.com/ProDevDenis/MyPortfolio.git
cd MyPortfolio

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
MyPortfolio/
├── docs/                     # Documentation
├── src/
│   ├── app/                  # Next.js App Router
│   ├── components/           # React Components
│   ├── lib/                  # Utilities & Configuration
│   └── styles/               # CSS Styles
├── public/                   # Static Assets
├── .github/workflows/        # GitHub Actions
├── tests/                    # Test Files
└── PROJECT_PLAN.md          # Project roadmap
```

## 🔄 Development Workflow

### Branch Strategy

- `main` - Production-ready code
- `develop` - Integration branch
- `feature/*` - Feature development
- `hotfix/*` - Critical fixes

### Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run type-check   # TypeScript checking
npm run format       # Format with Prettier

# Testing
npm run test         # Run tests
npm run test:watch   # Watch mode
npm run test:coverage # Coverage report
```

## 🚀 Deployment

### Automatic Deployment

- **Staging**: Deploys automatically on push to `develop`
- **Production**: Deploys automatically on push to `main`

### Manual Deployment

```bash
# Deploy to staging
vercel --target staging

# Deploy to production
vercel --prod
```

## 📊 Environment Variables

Required environment variables (see `.env.example`):

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 📚 Documentation

Comprehensive documentation is available in the [`docs/`](./docs/) directory:

### Core Documentation
- [Setup Guide](./docs/SETUP.md)
- [Improved Booking System](./docs/IMPROVED_BOOKING_SYSTEM.md) ⭐ **NEW**
- [Development Guide](./docs/DEVELOPMENT.md)
- [Deployment Guide](./docs/DEPLOYMENT.md)

### Booking System
- [Booking System Overview](./docs/BOOKING_SYSTEM.md)
- [Free Plan Setup](./docs/FREE_PLAN_SETUP.md)
- [Google Calendar Integration](./docs/GOOGLE_CALENDAR_SETUP.md) ⭐ **NEW**
- [Email Parsing Setup](./docs/EMAIL_PARSING_SETUP.md) ⭐ **NEW**

### Technical Reference
- [Component Library](./docs/COMPONENTS.md)
- [Database Schema](./docs/DATABASE.md)
- [API Documentation](./docs/API.md)

## 🆕 Recent Updates

### Version 1.3.0 (2025-01-03) ⭐ **MAJOR UPDATE**
- **🔄 Google Calendar Integration**: Automatic sync alternative to webhooks for free plan users
- **📧 Email Parsing System**: Extract booking data from Calendly email notifications
- **🎛️ Admin Dashboard**: Comprehensive booking management with manual entry capabilities
- **📊 Enhanced Analytics**: Free plan dashboard with booking statistics and insights
- **🔧 Multiple Sync Methods**: Webhook, Google Calendar, Email parsing, and manual options
- **✨ Improved Components**: Enhanced modal with success states and better error handling
- **🆓 Free Plan Optimization**: Full-featured experience without webhook limitations

### Version 1.2.0 (2025-01-03)
- **🎯 Calendly Booking System**: Complete integration with popup modals (works with free Calendly plan!)
- **🔗 Webhook Integration**: Automatic booking data sync (requires Calendly paid plan)
- **📊 Analytics Dashboard**: Comprehensive booking analytics and insights
- **🔒 Security Enhanced**: CSP headers configured for Calendly domains
- **📱 Mobile Optimized**: Responsive booking modal for all devices
- **💰 Free Plan Support**: Full functionality for Calendly free users

### Version 1.1.0 (2025-01-03)
- **Enhanced Mobile Navigation**: Completely separated mobile and desktop navigation experiences
- **Fixed Mobile Positioning**: Mobile navigation modal now properly displays all 5 navigation items
- **Improved UX**: Desktop shows clean text-only navigation, mobile shows icons with text
- **Better Responsive Design**: Optimized positioning for all screen sizes (25% top on default, 20% on small screens)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Contact

**Denis Erastus**

- Website: [deniserastus.com](https://deniserastus.com)
- Email: <EMAIL>
- GitHub: [@ProDevDenis](https://github.com/ProDevDenis)

---

_Built with ❤️ by Denis Erastus_
