# Current Calendly Implementation Status

## 📋 Overview

This document describes the current state of the Calendly booking system implementation in the portfolio website as of the latest development session.

## 🏗️ Current Architecture

### **Implementation Approach**
- **Type**: Complex modal-based integration with Calendly Inline Widget API
- **Method**: Uses `window.Calendly.initInlineWidget()` for widget initialization
- **State Management**: React hooks with loading, error, and success states
- **UI Pattern**: Custom modal with dark glass aesthetic

### **Key Components**

#### 1. CalendlyBookingModal Component
**Location**: `src/components/CalendlyBookingModal.tsx`

**Features**:
- ✅ Dynamic script loading when modal opens
- ✅ Loading skeleton with shimmer animation
- ✅ Error handling with fallback to external link
- ✅ Success state with auto-close after booking
- ✅ Escape key handling
- ✅ Body scroll lock when modal is open
- ✅ TypeScript interfaces for Calendly API

**Complete Component Code**:
```typescript
'use client'

import { useEffect, useState, useCallback } from 'react'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
}

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link'
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [calendlyLoaded, setCalendlyLoaded] = useState(false)

  // Load Calendly script dynamically
  useEffect(() => {
    if (!isOpen) return

    const loadCalendlyScript = () => {
      // Check if script is already loaded
      if (window.Calendly || document.querySelector('script[src*="calendly"]')) {
        setCalendlyLoaded(true)
        setIsLoading(false)
        return
      }

      const script = document.createElement('script')
      script.src = 'https://assets.calendly.com/assets/external/widget.js'
      script.async = true

      script.onload = () => {
        setCalendlyLoaded(true)
        setIsLoading(false)

        // Initialize Calendly widget
        if (window.Calendly) {
          try {
            window.Calendly.initInlineWidget({
              url: calendlyUrl,
              parentElement: document.getElementById('calendly-inline-widget'),
              prefill: {},
              utm: {
                utmCampaign: 'portfolio-website',
                utmSource: 'website',
                utmMedium: 'booking-modal'
              }
            })
          } catch (error) {
            console.error('Error initializing Calendly widget:', error)
            setHasError(true)
            setIsLoading(false)
          }
        }
      }

      script.onerror = () => {
        console.error('Failed to load Calendly script')
        setHasError(true)
        setIsLoading(false)
      }

      document.head.appendChild(script)
    }

    loadCalendlyScript()
  }, [isOpen, calendlyUrl])

  // Handle modal close
  const handleClose = useCallback(() => {
    onClose()
    // Reset states when modal closes
    setTimeout(() => {
      setIsLoading(true)
      setHasError(false)
    }, 300)
  }, [onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose])

  // Listen for Calendly events
  useEffect(() => {
    if (!isOpen || !calendlyLoaded) return

    const handleCalendlyEvent = (e: MessageEvent) => {
      if (e.origin !== 'https://calendly.com') return

      if (e.data.event === 'calendly.event_scheduled') {
        // Track booking completion
        console.log('Booking completed:', e.data)

        // Show success message for free plan users
        if (!process.env.CALENDLY_WEBHOOK_SECRET) {
          console.log('📅 Booking completed! Check your Calendly dashboard for details.')
        }

        // Close modal after successful booking
        setTimeout(() => {
          handleClose()
        }, 2000)
      }
    }

    window.addEventListener('message', handleCalendlyEvent)
    return () => window.removeEventListener('message', handleCalendlyEvent)
  }, [isOpen, calendlyLoaded, handleClose])

  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div
        className="calendly-modal-content"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule a Call</h2>
          <button
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {isLoading && (
            <div className="calendly-loading">
              <div className="calendly-skeleton">
                <div className="skeleton-header"></div>
                <div className="skeleton-calendar"></div>
                <div className="skeleton-times"></div>
              </div>
              <p>Loading calendar...</p>
            </div>
          )}

          {hasError && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try again or use the direct link below.</p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open in new tab
              </a>
            </div>
          )}

          {!isLoading && !hasError && (
            <div
              id="calendly-inline-widget"
              className="calendly-widget-container"
            />
          )}
        </div>
      </div>
    </div>
  )
}

// Extend Window interface for Calendly
declare global {
  interface Window {
    Calendly: {
      initInlineWidget: (options: {
        url: string
        parentElement: HTMLElement | null
        prefill?: Record<string, any>
        utm?: Record<string, string>
      }) => void
    }
  }
}
```

#### 2. Integration Points
**Components Updated**:
- ✅ `Hero.tsx` - Main CTA button
- ✅ `ContactModal.tsx` - Calendly card
- ✅ `Footer.tsx` - Footer booking link
- ✅ All use `useCalendlyModal` hook

**Hero Component Integration**:
```typescript
// src/components/Hero.tsx
'use client'

import CalendlyBookingModal from './CalendlyBookingModal'
import { useCalendlyModal } from '@/hooks/useCalendlyModal'

export default function Hero() {
  const { isOpen, openModal, closeModal } = useCalendlyModal()

  return (
    <section className="hero">
      {/* Hero content */}
      <div className="hero-cta">
        <Link href="#case-study" className="btn btn-primary">
          View Case Studies
        </Link>
        <button
          onClick={openModal}
          className="btn btn-secondary"
        >
          Book a Call
        </button>
      </div>

      {/* Calendly Booking Modal */}
      <CalendlyBookingModal
        isOpen={isOpen}
        onClose={closeModal}
      />
    </section>
  )
}
```

**ContactModal Component Integration**:
```typescript
// src/components/ContactModal.tsx
import CalendlyBookingModal from './CalendlyBookingModal'
import { useCalendlyModal } from '@/hooks/useCalendlyModal'

export default function ContactModal() {
  const { isOpen: isCalendlyOpen, openModal: openCalendlyModal, closeModal: closeCalendlyModal } = useCalendlyModal()

  return (
    <div className="contact-modal">
      {/* Other contact options */}

      <button
        onClick={openCalendlyModal}
        className="contact-card"
      >
        <h4>Book a 30-min Call</h4>
        <p>Free strategy session to discuss your needs</p>
      </button>

      {/* Calendly Booking Modal */}
      <CalendlyBookingModal
        isOpen={isCalendlyOpen}
        onClose={closeCalendlyModal}
      />
    </div>
  )
}
```

**Footer Component Integration**:
```typescript
// src/components/Footer.tsx
'use client'

import CalendlyBookingModal from './CalendlyBookingModal'
import { useCalendlyModal } from '@/hooks/useCalendlyModal'

export default function Footer() {
  const { isOpen, openModal, closeModal } = useCalendlyModal()

  return (
    <footer>
      {/* Footer content */}
      <li>
        <button onClick={openModal} className="footer-link-btn">
          Book a Call
        </button>
      </li>

      {/* Calendly Booking Modal */}
      <CalendlyBookingModal
        isOpen={isOpen}
        onClose={closeModal}
      />
    </footer>
  )
}
```

#### 3. Custom Hook
**Location**: `src/hooks/useCalendlyModal.ts`
```typescript
'use client'

import { useState, useCallback } from 'react'

export function useCalendlyModal() {
  const [isOpen, setIsOpen] = useState(false)

  const openModal = useCallback(() => {
    setIsOpen(true)
  }, [])

  const closeModal = useCallback(() => {
    setIsOpen(false)
  }, [])

  const toggleModal = useCallback(() => {
    setIsOpen(prev => !prev)
  }, [])

  return {
    isOpen,
    openModal,
    closeModal,
    toggleModal
  }
}
```

## 🎨 Styling & UI

### **Modal Design**
- **Background**: Dark glass panels with backdrop blur
- **Colors**: Cyan accents matching portfolio theme
- **Animations**: Fade in overlay, slide up modal
- **Responsive**: Mobile-optimized sizing

### **Loading States**
- **Skeleton Animation**: Shimmer effect for calendar loading
- **Error State**: Icon with fallback link to external Calendly
- **Success State**: Auto-close after 2 seconds

### **CSS Classes**
**Location**: `src/app/globals.css`

```css
/* ============================================
   CALENDLY BOOKING MODAL STYLES
   ============================================ */

.calendly-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

.calendly-modal-content {
  background: var(--bg-secondary);
  border: 1px solid rgba(148, 163, 184, 0.2);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

.calendly-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 32px;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  background: var(--bg-glass);
}

.calendly-modal-header h2 {
  color: var(--text-white);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
}

.calendly-close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.calendly-close-btn:hover {
  color: var(--primary-cyan);
  background: var(--bg-transparent);
  box-shadow: var(--glow-subtle);
}

.calendly-modal-body {
  padding: 0;
  height: 600px;
  overflow: hidden;
}

.calendly-widget-container {
  width: 100%;
  height: 100%;
  min-height: 600px;
}

/* Loading States */
.calendly-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
  gap: 24px;
}

.calendly-skeleton {
  width: 100%;
  max-width: 400px;
  padding: 32px;
}

.skeleton-header,
.skeleton-calendar,
.skeleton-times {
  background: linear-gradient(90deg,
    rgba(148, 163, 184, 0.1) 25%,
    rgba(148, 163, 184, 0.2) 50%,
    rgba(148, 163, 184, 0.1) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 8px;
  margin-bottom: 16px;
}

.skeleton-header {
  height: 32px;
  width: 60%;
}

.skeleton-calendar {
  height: 200px;
  width: 100%;
}

.skeleton-times {
  height: 120px;
  width: 100%;
}

/* Error States */
.calendly-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 48px 32px;
  color: var(--text-secondary);
}

.error-icon {
  color: #ef4444;
  margin-bottom: 24px;
}

.calendly-error h3 {
  color: var(--text-white);
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.calendly-error p {
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.calendly-fallback-btn {
  background: linear-gradient(135deg, var(--primary-cyan), var(--dark-cyan));
  color: var(--text-white);
  padding: 12px 24px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-block;
}

.calendly-fallback-btn:hover {
  background: linear-gradient(135deg, var(--dark-cyan), #0e7490);
  transform: translateY(-1px);
  box-shadow: var(--glow-cyan);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Footer Link Button Styles */
.footer-link-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  font-size: inherit;
  font-family: inherit;
  text-decoration: none;
  transition: color 0.2s ease;
}

.footer-link-btn:hover {
  color: var(--primary-cyan);
}

/* Mobile Responsiveness for Calendly Modal */
@media (max-width: 768px) {
  .calendly-modal-overlay {
    padding: 16px;
  }

  .calendly-modal-content {
    max-height: 95vh;
    border-radius: 12px;
  }

  .calendly-modal-header {
    padding: 20px 24px;
  }

  .calendly-modal-header h2 {
    font-size: 1.25rem;
  }

  .calendly-modal-body {
    height: 500px;
  }

  .calendly-widget-container {
    min-height: 500px;
  }

  .calendly-error {
    padding: 32px 24px;
  }
}
```

## 🔧 Configuration

### **Environment Variables**
```env
# Current Configuration
NEXT_PUBLIC_CALENDLY_URL=https://calendly.com/denis-aidev/30min
CALENDLY_WEBHOOK_SECRET=your_webhook_secret_when_you_upgrade
```

### **CSP Headers**
**Location**: `next.config.js`
```javascript
"script-src 'self' 'unsafe-eval' 'unsafe-inline' https://assets.calendly.com https://calendly.com"
"frame-src 'self' https://calendly.com https://*.calendly.com"
"style-src 'self' 'unsafe-inline' https://assets.calendly.com"
```

## 🔄 Event Handling

### **Calendly Events**
```typescript
const handleCalendlyEvent = (e: MessageEvent) => {
  if (e.origin !== 'https://calendly.com') return
  
  if (e.data.event === 'calendly.event_scheduled') {
    console.log('Booking completed:', e.data)
    setTimeout(() => handleClose(), 2000)
  }
}
```

### **Modal Events**
- **Escape Key**: Closes modal
- **Overlay Click**: Closes modal
- **Close Button**: Closes modal
- **Booking Complete**: Auto-closes after 2 seconds

## 🗄️ Database Integration

### **Supabase Setup**
- ✅ **Database Schema**: Complete bookings table created
- ✅ **RLS Policies**: Row Level Security configured
- ✅ **Helper Functions**: Database operations in `src/lib/supabase.ts`
- ✅ **Analytics View**: Booking insights and reporting

### **Webhook Infrastructure**
- ✅ **API Route**: `/api/calendly/webhook` for webhook handling
- ✅ **Free Plan Support**: Graceful handling when webhook secret is missing
- ❌ **Active Webhooks**: Not configured (requires Calendly paid plan)

**Webhook API Route Code**:
**Location**: `src/app/api/calendly/webhook/route.ts`

```typescript
import { NextRequest, NextResponse } from 'next/server'
import crypto from 'crypto'
import { db } from '@/lib/supabase'

// Calendly webhook event types we handle
const SUPPORTED_EVENTS = [
  'invitee.created',
  'invitee.canceled',
  'invitee_no_show.created'
]

// Verify Calendly webhook signature
function verifyWebhookSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  try {
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('base64')

    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    )
  } catch (error) {
    console.error('Error verifying webhook signature:', error)
    return false
  }
}

// Parse Calendly webhook payload
function parseCalendlyPayload(payload: any) {
  const event = payload.event
  const invitee = payload.payload?.invitee
  const eventDetails = payload.payload?.event

  if (!event || !invitee || !eventDetails) {
    throw new Error('Invalid Calendly webhook payload structure')
  }

  return {
    calendly_id: eventDetails.uuid,
    event_type: eventDetails.event_type?.name || 'Unknown',
    event_name: eventDetails.name,
    invitee_email: invitee.email,
    invitee_name: invitee.name,
    invitee_timezone: invitee.timezone,
    scheduled_at: eventDetails.start_time,
    start_time: eventDetails.start_time,
    end_time: eventDetails.end_time,
    status: event === 'invitee.canceled' ? 'canceled' as const :
            event === 'invitee_no_show.created' ? 'no_show' as const :
            'active' as const,
    meeting_url: eventDetails.location?.join_url || eventDetails.location?.location,
    location: eventDetails.location?.type || eventDetails.location?.location,
    raw_data: payload
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the webhook secret from environment variables
    const webhookSecret = process.env.CALENDLY_WEBHOOK_SECRET
    if (!webhookSecret) {
      console.log('CALENDLY_WEBHOOK_SECRET not configured - Free plan detected')
      return NextResponse.json(
        {
          message: 'Webhook functionality requires Calendly paid plan (Essentials or higher)',
          current_plan: 'free',
          alternatives: [
            'Manual data export from Calendly dashboard',
            'Email notification parsing with Zapier',
            'Google Calendar API integration',
            'Upgrade to Calendly Essentials plan ($8/month)'
          ],
          documentation: 'See docs/FREE_PLAN_SETUP.md for alternatives'
        },
        { status: 200 }
      )
    }

    // Get the raw body and signature
    const body = await request.text()
    const signature = request.headers.get('calendly-webhook-signature')

    if (!signature) {
      console.error('Missing Calendly webhook signature')
      return NextResponse.json(
        { error: 'Missing webhook signature' },
        { status: 400 }
      )
    }

    // Verify the webhook signature
    if (!verifyWebhookSignature(body, signature, webhookSecret)) {
      console.error('Invalid Calendly webhook signature')
      return NextResponse.json(
        { error: 'Invalid webhook signature' },
        { status: 401 }
      )
    }

    // Parse the JSON payload
    const payload = JSON.parse(body)
    const eventType = payload.event

    // Check if we support this event type
    if (!SUPPORTED_EVENTS.includes(eventType)) {
      console.log(`Unsupported event type: ${eventType}`)
      return NextResponse.json(
        { message: 'Event type not supported' },
        { status: 200 }
      )
    }

    // Parse the Calendly data
    const bookingData = parseCalendlyPayload(payload)

    // Handle different event types
    switch (eventType) {
      case 'invitee.created':
        // Create new booking
        await db.bookings.upsert(bookingData)
        console.log(`New booking created: ${bookingData.calendly_id}`)
        break

      case 'invitee.canceled':
        // Update booking status to canceled
        await db.bookings.updateStatus(bookingData.calendly_id, 'canceled')
        console.log(`Booking canceled: ${bookingData.calendly_id}`)
        break

      case 'invitee_no_show.created':
        // Update booking status to no_show
        await db.bookings.updateStatus(bookingData.calendly_id, 'no_show')
        console.log(`Booking marked as no-show: ${bookingData.calendly_id}`)
        break

      default:
        console.log(`Unhandled event type: ${eventType}`)
    }

    // Return success response
    return NextResponse.json(
      {
        message: 'Webhook processed successfully',
        event: eventType,
        calendly_id: bookingData.calendly_id
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Error processing Calendly webhook:', error)

    // Return error response
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle GET requests (for webhook verification)
export async function GET() {
  return NextResponse.json(
    { message: 'Calendly webhook endpoint is active' },
    { status: 200 }
  )
}
```

## ⚠️ Current Issues

### **Primary Problem**
**Calendar Not Displaying**: The Calendly widget is not rendering in the modal despite:
- ✅ Script loading successfully
- ✅ Modal opening correctly
- ✅ No console errors
- ✅ Proper CSP headers

### **Potential Causes**
1. **Complex State Management**: Multiple loading states may interfere with widget initialization
2. **API Initialization**: Using `initInlineWidget()` instead of simple `data-url` approach
3. **Timing Issues**: Widget initialization may occur before DOM is ready
4. **Modal Rendering**: Custom modal structure may conflict with Calendly expectations

### **Debugging Attempts**
- ✅ Verified script loading
- ✅ Checked CSP headers
- ✅ Confirmed environment variables
- ✅ Tested with simple HTML structure
- ❌ Widget still not displaying

## 🎯 Recommended Next Steps

### **Option 1: Simplify to Sample Pattern**
Follow the exact pattern from `docs/sample-calendly-setup.md`:
- Remove complex loading states
- Use simple `data-url` attribute approach
- Simplify modal structure
- Remove API initialization

### **Option 2: Debug Current Implementation**
- Add more detailed logging
- Test widget initialization timing
- Verify DOM element availability
- Check for JavaScript conflicts

### **Option 3: Hybrid Approach**
- Keep custom styling
- Use simple widget initialization
- Maintain error handling
- Remove complex state management

## 📊 Implementation Metrics

### **Code Complexity**
- **Component Size**: 217 lines (complex)
- **State Variables**: 3 (isLoading, hasError, calendlyLoaded)
- **useEffect Hooks**: 3 (script loading, escape key, events)
- **Error Handling**: Comprehensive with fallbacks

### **Features Implemented**
- ✅ Modal popup system
- ✅ Loading states
- ✅ Error handling
- ✅ Event tracking
- ✅ Mobile responsiveness
- ✅ Accessibility features
- ❌ **Calendar display** (main issue)

## 🔍 Technical Analysis

### **Strengths**
- Comprehensive error handling
- Professional UI/UX design
- Mobile-responsive
- Accessibility compliant
- TypeScript support
- Proper state management

### **Weaknesses**
- Overcomplicated for basic widget display
- Multiple potential failure points
- Complex initialization logic
- Not following Calendly's recommended simple approach

## 💡 Conclusion

The current implementation is **feature-rich but non-functional** due to the calendar not displaying. The architecture is solid but overcomplicated for the core requirement of showing a Calendly widget in a modal.

**Recommendation**: Simplify to the basic pattern from the sample file while maintaining the custom styling and modal structure. This would provide the best balance of functionality and maintainability.
