/**
 * Email Notification Parser for Calendly
 * 
 * This module provides functionality to parse Calendly email notifications
 * and extract booking data for free plan users.
 * 
 * Supported email types:
 * - New booking confirmations
 * - Booking cancellations
 * - Booking reschedules
 */

import { db } from './supabase'

interface ParsedBookingData {
  invitee_name: string
  invitee_email: string
  event_type: string
  event_name: string
  start_time: string
  end_time: string
  timezone: string
  location: string
  calendly_link?: string
  meeting_url?: string
  status: 'active' | 'canceled' | 'rescheduled'
}

interface EmailParseResult {
  success: boolean
  data?: ParsedBookingData
  error?: string
  confidence: number // 0-1 score of parsing confidence
}

class CalendlyEmailParser {
  
  /**
   * Parse Calendly email content and extract booking data
   */
  static parseEmail(emailContent: string, subject: string): EmailParseResult {
    try {
      // Clean up email content
      const cleanContent = this.cleanEmailContent(emailContent)
      
      // Determine email type
      const emailType = this.determineEmailType(subject, cleanContent)
      
      if (emailType === 'unknown') {
        return {
          success: false,
          error: 'Email does not appear to be a Calendly notification',
          confidence: 0
        }
      }

      // Extract booking data based on email type
      const bookingData = this.extractBookingData(cleanContent, emailType)
      
      if (!bookingData) {
        return {
          success: false,
          error: 'Could not extract booking data from email',
          confidence: 0.2
        }
      }

      // Calculate confidence score
      const confidence = this.calculateConfidence(bookingData, cleanContent)

      return {
        success: true,
        data: bookingData,
        confidence
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown parsing error',
        confidence: 0
      }
    }
  }

  /**
   * Clean email content by removing HTML tags and normalizing whitespace
   */
  private static cleanEmailContent(content: string): string {
    return content
      .replace(/<[^>]*>/g, ' ') // Remove HTML tags
      .replace(/\s+/g, ' ') // Normalize whitespace
      .replace(/&nbsp;/g, ' ') // Replace HTML entities
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .trim()
  }

  /**
   * Determine the type of Calendly email
   */
  private static determineEmailType(subject: string, content: string): string {
    const lowerSubject = subject.toLowerCase()
    const lowerContent = content.toLowerCase()

    if (lowerSubject.includes('confirmed') || lowerContent.includes('event confirmed')) {
      return 'booking_confirmed'
    }
    
    if (lowerSubject.includes('canceled') || lowerContent.includes('event canceled')) {
      return 'booking_canceled'
    }
    
    if (lowerSubject.includes('rescheduled') || lowerContent.includes('rescheduled')) {
      return 'booking_rescheduled'
    }

    if (lowerContent.includes('calendly') && (
        lowerContent.includes('meeting') || 
        lowerContent.includes('appointment') ||
        lowerContent.includes('call')
    )) {
      return 'booking_confirmed' // Default to confirmed for Calendly emails
    }

    return 'unknown'
  }

  /**
   * Extract booking data from email content
   */
  private static extractBookingData(content: string, emailType: string): ParsedBookingData | null {
    try {
      const data: Partial<ParsedBookingData> = {}

      // Extract invitee name
      const nameMatch = content.match(/(?:with|from|by)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i)
      if (nameMatch) {
        data.invitee_name = nameMatch[1].trim()
      }

      // Extract invitee email
      const emailMatch = content.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g)
      if (emailMatch && emailMatch.length > 0) {
        // Filter out common system emails
        const userEmail = emailMatch.find(email => 
          !email.includes('calendly.com') && 
          !email.includes('noreply') &&
          !email.includes('no-reply')
        )
        if (userEmail) {
          data.invitee_email = userEmail
        }
      }

      // Extract event name/type
      const eventMatch = content.match(/(?:Event Type:|Event:|Meeting:)\s*([^\n\r]+)/i)
      if (eventMatch) {
        data.event_name = eventMatch[1].trim()
        data.event_type = this.normalizeEventType(eventMatch[1])
      }

      // Extract date and time
      const dateTimeMatch = content.match(/(?:Date & Time:|When:)\s*([^\n\r]+)/i)
      if (dateTimeMatch) {
        const dateTimeStr = dateTimeMatch[1].trim()
        const parsedDateTime = this.parseDateTimeString(dateTimeStr)
        if (parsedDateTime) {
          data.start_time = parsedDateTime.start
          data.end_time = parsedDateTime.end
          data.timezone = parsedDateTime.timezone
        }
      }

      // Extract location
      const locationMatch = content.match(/(?:Location:|Where:)\s*([^\n\r]+)/i)
      if (locationMatch) {
        data.location = locationMatch[1].trim()
      } else {
        data.location = 'Online' // Default for most Calendly meetings
      }

      // Extract meeting URL
      const urlMatch = content.match(/(https?:\/\/[^\s]+(?:zoom|meet|teams|calendly)[^\s]*)/i)
      if (urlMatch) {
        data.meeting_url = urlMatch[1]
      }

      // Set status based on email type
      data.status = emailType === 'booking_canceled' ? 'canceled' : 'active'

      // Validate required fields
      if (!data.invitee_email || !data.start_time) {
        return null
      }

      return data as ParsedBookingData

    } catch (error) {
      console.error('Error extracting booking data:', error)
      return null
    }
  }

  /**
   * Parse date/time string from email
   */
  private static parseDateTimeString(dateTimeStr: string): { start: string, end: string, timezone: string } | null {
    try {
      // Common patterns in Calendly emails
      const patterns = [
        // "Monday, January 15, 2024 at 2:00pm - 2:30pm (EST)"
        /(\w+,\s+\w+\s+\d+,\s+\d+)\s+at\s+(\d+:\d+\w+)\s*-\s*(\d+:\d+\w+)\s*\(([^)]+)\)/i,
        // "January 15, 2024 2:00pm - 2:30pm EST"
        /(\w+\s+\d+,\s+\d+)\s+(\d+:\d+\w+)\s*-\s*(\d+:\d+\w+)\s+([A-Z]{3,4})/i,
        // "2024-01-15 14:00 - 14:30 UTC"
        /(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2})\s*-\s*(\d{2}:\d{2})\s+([A-Z]{3,4})/i
      ]

      for (const pattern of patterns) {
        const match = dateTimeStr.match(pattern)
        if (match) {
          const [, date, startTime, endTime, timezone] = match
          
          // Convert to ISO format
          const startDateTime = new Date(`${date} ${startTime}`)
          const endDateTime = new Date(`${date} ${endTime}`)
          
          if (!isNaN(startDateTime.getTime()) && !isNaN(endDateTime.getTime())) {
            return {
              start: startDateTime.toISOString(),
              end: endDateTime.toISOString(),
              timezone: timezone.trim()
            }
          }
        }
      }

      return null
    } catch (error) {
      console.error('Error parsing date/time:', error)
      return null
    }
  }

  /**
   * Normalize event type from event name
   */
  private static normalizeEventType(eventName: string): string {
    const lower = eventName.toLowerCase()
    
    if (lower.includes('consultation')) return 'consultation'
    if (lower.includes('discovery')) return 'discovery'
    if (lower.includes('strategy')) return 'strategy'
    if (lower.includes('demo')) return 'demo'
    if (lower.includes('30')) return '30-minute-call'
    if (lower.includes('60')) return '60-minute-call'
    if (lower.includes('follow')) return 'follow-up'
    
    return 'meeting'
  }

  /**
   * Calculate confidence score for parsed data
   */
  private static calculateConfidence(data: ParsedBookingData, content: string): number {
    let score = 0.5 // Base score

    // Check for required fields
    if (data.invitee_email) score += 0.2
    if (data.invitee_name) score += 0.1
    if (data.start_time) score += 0.2
    if (data.event_name) score += 0.1

    // Check for Calendly-specific content
    if (content.toLowerCase().includes('calendly')) score += 0.1
    if (content.toLowerCase().includes('event confirmed')) score += 0.1
    if (data.meeting_url) score += 0.1

    return Math.min(score, 1.0)
  }

  /**
   * Save parsed booking data to Supabase
   */
  static async saveBookingFromEmail(emailData: ParsedBookingData): Promise<boolean> {
    try {
      const bookingData = {
        calendly_id: `email-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        event_type: emailData.event_type,
        event_name: emailData.event_name,
        invitee_email: emailData.invitee_email,
        invitee_name: emailData.invitee_name,
        invitee_timezone: emailData.timezone,
        scheduled_at: new Date().toISOString(),
        start_time: emailData.start_time,
        end_time: emailData.end_time,
        status: emailData.status,
        meeting_url: emailData.meeting_url || '',
        location: emailData.location,
        raw_data: { source: 'email_parser', parsed_data: emailData }
      }

      await db.bookings.upsert(bookingData)
      console.log('✅ Booking saved from email:', bookingData.invitee_email)
      return true

    } catch (error) {
      console.error('❌ Error saving booking from email:', error)
      return false
    }
  }
}

export { CalendlyEmailParser, type ParsedBookingData, type EmailParseResult }
export default CalendlyEmailParser
