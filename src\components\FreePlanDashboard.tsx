'use client'

import { useState, useEffect } from 'react'

interface FreePlanStats {
  totalBookings: number
  thisWeekBookings: number
  pendingEmails: number
  lastSync: string | null
  googleCalendarConfigured: boolean
  webhookConfigured: boolean
}

export default function FreePlanDashboard() {
  const [stats, setStats] = useState<FreePlanStats>({
    totalBookings: 0,
    thisWeekBookings: 0,
    pendingEmails: 0,
    lastSync: null,
    googleCalendarConfigured: false,
    webhookConfigured: false
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch booking stats
      const bookingsResponse = await fetch('/api/bookings')
      const bookingsData = await bookingsResponse.json()
      
      // Fetch Google Calendar status
      const gcalResponse = await fetch('/api/sync/google-calendar')
      const gcalData = await gcalResponse.json()

      // Calculate stats
      const bookings = bookingsData.bookings || []
      const oneWeekAgo = new Date()
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
      
      const thisWeekBookings = bookings.filter((booking: any) => 
        new Date(booking.created_at) > oneWeekAgo
      ).length

      setStats({
        totalBookings: bookings.length,
        thisWeekBookings,
        pendingEmails: 0, // TODO: Implement email queue
        lastSync: gcalData.last_sync,
        googleCalendarConfigured: gcalData.google_calendar_configured || false,
        webhookConfigured: !!process.env.CALENDLY_WEBHOOK_SECRET
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const syncGoogleCalendar = async () => {
    try {
      const response = await fetch('/api/sync/google-calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ daysBack: 7 })
      })

      if (response.ok) {
        alert('Sync completed successfully!')
        fetchStats()
      } else {
        const error = await response.json()
        alert(`Sync failed: ${error.message}`)
      }
    } catch (error) {
      alert('Error syncing calendar')
    }
  }

  if (loading) {
    return (
      <div className="free-plan-dashboard loading">
        <div className="loading-spinner"></div>
        <p>Loading dashboard...</p>
      </div>
    )
  }

  return (
    <div className="free-plan-dashboard">
      <div className="dashboard-header">
        <h2>📅 Booking Dashboard</h2>
        <div className="plan-badge">
          {stats.webhookConfigured ? (
            <span className="badge badge-pro">Pro Plan</span>
          ) : (
            <span className="badge badge-free">Free Plan</span>
          )}
        </div>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>{stats.totalBookings}</h3>
            <p>Total Bookings</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <h3>{stats.thisWeekBookings}</h3>
            <p>This Week</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            {stats.googleCalendarConfigured ? '✅' : '⚠️'}
          </div>
          <div className="stat-content">
            <h3>{stats.googleCalendarConfigured ? 'Connected' : 'Not Setup'}</h3>
            <p>Google Calendar</p>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-icon">
            {stats.webhookConfigured ? '🔗' : '📧'}
          </div>
          <div className="stat-content">
            <h3>{stats.webhookConfigured ? 'Active' : 'Email Only'}</h3>
            <p>Data Sync</p>
          </div>
        </div>
      </div>

      {!stats.webhookConfigured && (
        <div className="free-plan-info">
          <div className="info-header">
            <h3>🆓 Free Plan Features</h3>
            <p>You're using Calendly's free plan. Here's what's available:</p>
          </div>

          <div className="features-grid">
            <div className="feature-card active">
              <div className="feature-icon">✅</div>
              <div className="feature-content">
                <h4>Popup Booking Modal</h4>
                <p>Beautiful, seamless booking experience</p>
              </div>
            </div>

            <div className="feature-card active">
              <div className="feature-icon">✅</div>
              <div className="feature-content">
                <h4>Manual Booking Tracking</h4>
                <p>Add bookings manually for record keeping</p>
              </div>
            </div>

            <div className="feature-card active">
              <div className="feature-icon">✅</div>
              <div className="feature-content">
                <h4>Email Parsing</h4>
                <p>Extract booking data from Calendly emails</p>
              </div>
            </div>

            <div className={`feature-card ${stats.googleCalendarConfigured ? 'active' : 'inactive'}`}>
              <div className="feature-icon">
                {stats.googleCalendarConfigured ? '✅' : '⚙️'}
              </div>
              <div className="feature-content">
                <h4>Google Calendar Sync</h4>
                <p>Automatic sync from Google Calendar</p>
                {!stats.googleCalendarConfigured && (
                  <a href="/docs/GOOGLE_CALENDAR_SETUP.md" className="setup-link">
                    Setup Guide →
                  </a>
                )}
              </div>
            </div>
          </div>

          <div className="upgrade-info">
            <h4>🚀 Want Automatic Webhooks?</h4>
            <p>
              Upgrade to Calendly Essentials ($8/month) for automatic real-time booking sync.
              <br />
              <a href="https://calendly.com/pricing" target="_blank" rel="noopener noreferrer">
                View Calendly Pricing →
              </a>
            </p>
          </div>
        </div>
      )}

      <div className="quick-actions">
        <h3>⚡ Quick Actions</h3>
        <div className="actions-grid">
          <a href="/admin/bookings" className="action-card">
            <div className="action-icon">📝</div>
            <div className="action-content">
              <h4>Manage Bookings</h4>
              <p>View, add, and edit bookings</p>
            </div>
          </a>

          {stats.googleCalendarConfigured && (
            <button onClick={syncGoogleCalendar} className="action-card">
              <div className="action-icon">🔄</div>
              <div className="action-content">
                <h4>Sync Calendar</h4>
                <p>Update from Google Calendar</p>
              </div>
            </button>
          )}

          <a href="/docs/FREE_PLAN_SETUP.md" className="action-card">
            <div className="action-icon">📚</div>
            <div className="action-content">
              <h4>Setup Guide</h4>
              <p>Optimize your free plan setup</p>
            </div>
          </a>

          <a href="/docs/EMAIL_PARSING_SETUP.md" className="action-card">
            <div className="action-icon">📧</div>
            <div className="action-content">
              <h4>Email Parsing</h4>
              <p>Automate with email forwarding</p>
            </div>
          </a>
        </div>
      </div>

      <div className="tips-section">
        <h3>💡 Pro Tips for Free Plan Users</h3>
        <div className="tips-list">
          <div className="tip">
            <span className="tip-icon">📧</span>
            <p>Set up email forwarding to automatically parse Calendly notifications</p>
          </div>
          <div className="tip">
            <span className="tip-icon">📅</span>
            <p>Connect Calendly to Google Calendar for automatic event creation</p>
          </div>
          <div className="tip">
            <span className="tip-icon">⏰</span>
            <p>Check your booking dashboard weekly to stay on top of appointments</p>
          </div>
          <div className="tip">
            <span className="tip-icon">📊</span>
            <p>Export booking data monthly for business analytics and reporting</p>
          </div>
        </div>
      </div>
    </div>
  )
}
