## Popup
import { useEffect } from 'react';
import Script from 'next/script';

const CalendlyPopup = ({ 
  url = "https://calendly.com/YOUR_USERNAME/YOUR_EVENT",
  buttonText = "Book a Meeting",
  buttonClassName = "bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded",
  prefill = {}, // { name: '', email: '', customAnswers: {} }
  utm = {} // { utmCampaign: '', utmSource: '', utmMedium: '', utmContent: '', utmTerm: '' }
}) => {
  useEffect(() => {
    // Listen for Calendly events
    const handleCalendlyEvent = (e) => {
      if (e.data.event && e.data.event.indexOf('calendly') === 0) {
        console.log('Calendly Event:', e.data);
        
        // Handle different Calendly events
        if (e.data.event === 'calendly.event_scheduled') {
          // Event was successfully scheduled
          const eventData = {
            event_uri: e.data.payload.event.uri,
            invitee_uri: e.data.payload.invitee.uri,
            invitee_name: e.data.payload.invitee.name,
            invitee_email: e.data.payload.invitee.email,
            scheduled_at: new Date().toISOString(),
            // Add any custom fields data here
          };
          
          // Send to your API to store in Supabase
          saveBookingToSupabase(eventData);
        }
      }
    };

    window.addEventListener('message', handleCalendlyEvent);
    
    return () => {
      window.removeEventListener('message', handleCalendlyEvent);
    };
  }, []);

  const saveBookingToSupabase = async (bookingData) => {
    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      });
      
      if (response.ok) {
        console.log('Booking saved to Supabase');
        // You can add success notification here
      }
    } catch (error) {
      console.error('Error saving booking:', error);
    }
  };

  const openCalendlyPopup = () => {
    if (window.Calendly) {
      window.Calendly.initPopupWidget({
        url: url,
        prefill: prefill,
        utm: utm,
      });
    }
  };

  return (
    <>
      <Script
        src="https://assets.calendly.com/assets/external/widget.js"
        strategy="lazyOnload"
        onLoad={() => {
          console.log('Calendly script loaded');
        }}
      />
      
      <button
        onClick={openCalendlyPopup}
        className={buttonClassName}
      >
        {buttonText}
      </button>
    </>
  );
};

export default CalendlyPopup;

## API Route to Store in Supabase
// pages/api/bookings.js or app/api/bookings/route.js (for App Router)
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY // Use service key for server-side
);

export default async function handler(req, res) {
  if (req.method === 'POST') {
    try {
      const bookingData = req.body;
      
      // Insert booking into Supabase
      const { data, error } = await supabase
        .from('bookings')
        .insert([
          {
            event_uri: bookingData.event_uri,
            invitee_uri: bookingData.invitee_uri,
            invitee_name: bookingData.invitee_name,
            invitee_email: bookingData.invitee_email,
            scheduled_at: bookingData.scheduled_at,
            custom_fields: bookingData.custom_fields || {},
            created_at: new Date().toISOString(),
          }
        ]);

      if (error) throw error;

      // Optionally, fetch additional data from Calendly using their webhooks
      // or send confirmation emails, etc.

      res.status(200).json({ success: true, data });
    } catch (error) {
      console.error('Error saving booking:', error);
      res.status(500).json({ error: 'Failed to save booking' });
    }
  } else {
    res.setHeader('Allow', ['POST']);
    res.status(405).end(`Method ${req.method} Not Allowed`);
  }
}

// For Next.js 13+ App Router
// export async function POST(request) {
//   try {
//     const bookingData = await request.json();
//     // ... same logic as above
//     return Response.json({ success: true, data });
//   } catch (error) {
//     return Response.json({ error: 'Failed to save booking' }, { status: 500 });
//   }
// }

##  Supabase Table Schema
-- Create bookings table in Supabase
CREATE TABLE bookings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  event_uri TEXT NOT NULL,
  invitee_uri TEXT NOT NULL,
  invitee_name TEXT NOT NULL,
  invitee_email TEXT NOT NULL,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  custom_fields JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX idx_bookings_email ON bookings(invitee_email);
CREATE INDEX idx_bookings_scheduled_at ON bookings(scheduled_at);

-- Enable Row Level Security (optional but recommended)
ALTER TABLE bookings ENABLE ROW LEVEL SECURITY;

-- Create a policy (adjust based on your needs)
CREATE POLICY "Enable insert for authenticated users only" ON bookings
  FOR INSERT TO authenticated
  WITH CHECK (true);

CREATE POLICY "Enable read for authenticated users only" ON bookings
  FOR SELECT TO authenticated
  USING (true);

  ## Usage Example
  import CalendlyPopup from '../components/CalendlyPopup';

export default function BookingPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-3xl mx-auto text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Book a Consultation
        </h1>
        <p className="text-xl text-gray-600 mb-8">
          Schedule a free 30-minute consultation with our team
        </p>
        
        {/* Simple implementation */}
        <CalendlyPopup 
          url="https://calendly.com/YOUR_USERNAME/30min"
          buttonText="Schedule Now"
        />
        
        {/* Advanced implementation with prefill */}
        <div className="mt-8">
          <CalendlyPopup 
            url="https://calendly.com/YOUR_USERNAME/consultation"
            buttonText="Book Premium Consultation"
            buttonClassName="bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg text-lg"
            prefill={{
              name: "John Doe", // You can get this from user session
              email: "<EMAIL>",
              customAnswers: {
                a1: "Product Demo", // Answer to your custom question
              }
            }}
            utm={{
              utmSource: "website",
              utmMedium: "cta",
              utmCampaign: "consultation"
            }}
          />
        </div>
      </div>
    </div>
  );
}

Customize the css