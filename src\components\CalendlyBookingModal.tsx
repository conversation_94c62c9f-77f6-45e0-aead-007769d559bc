'use client'

import { useEffect, useState, useCallback } from 'react'

interface CalendlyBookingModalProps {
  isOpen: boolean
  onClose: () => void
  calendlyUrl?: string
  prefill?: {
    name?: string
    email?: string
    customAnswers?: Record<string, string>
  }
  utm?: {
    utmCampaign?: string
    utmSource?: string
    utmMedium?: string
    utmContent?: string
    utmTerm?: string
  }
}

export default function CalendlyBookingModal({
  isOpen,
  onClose,
  calendlyUrl = process.env.NEXT_PUBLIC_CALENDLY_URL || 'https://calendly.com/your-scheduling-link',
  prefill = {},
  utm = {
    utmCampaign: 'portfolio-website',
    utmSource: 'website',
    utmMedium: 'booking-modal'
  }
}: CalendlyBookingModalProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [calendlyLoaded, setCalendlyLoaded] = useState(false)
  const [bookingSuccess, setBookingSuccess] = useState(false)

  // Load Calendly script dynamically
  useEffect(() => {
    if (!isOpen) return

    const loadCalendlyScript = () => {
      // Check if script is already loaded
      if (window.Calendly || document.querySelector('script[src*="calendly"]')) {
        setCalendlyLoaded(true)
        setIsLoading(false)
        return
      }

      const script = document.createElement('script')
      script.src = 'https://assets.calendly.com/assets/external/widget.js'
      script.async = true
      
      script.onload = () => {
        setCalendlyLoaded(true)
        setIsLoading(false)
        
        // Initialize Calendly widget with enhanced options
        if (window.Calendly) {
          try {
            window.Calendly.initInlineWidget({
              url: calendlyUrl,
              parentElement: document.getElementById('calendly-inline-widget'),
              prefill: prefill,
              utm: utm
            })
          } catch (error) {
            console.error('Error initializing Calendly widget:', error)
            setHasError(true)
            setIsLoading(false)
          }
        }
      }

      script.onerror = () => {
        console.error('Failed to load Calendly script')
        setHasError(true)
        setIsLoading(false)
      }

      document.head.appendChild(script)
    }

    loadCalendlyScript()
  }, [isOpen, calendlyUrl])

  // Handle modal close
  const handleClose = useCallback(() => {
    onClose()
    // Reset states when modal closes
    setTimeout(() => {
      setIsLoading(true)
      setHasError(false)
    }, 300)
  }, [onClose])

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        handleClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'auto'
    }
  }, [isOpen, handleClose])

  // Enhanced Calendly event handling with Supabase integration
  useEffect(() => {
    if (!isOpen || !calendlyLoaded) return

    const handleCalendlyEvent = async (e: MessageEvent) => {
      if (e.origin !== 'https://calendly.com') return

      console.log('Calendly Event:', e.data)

      if (e.data.event === 'calendly.event_scheduled') {
        // Track booking completion
        console.log('Booking completed:', e.data)

        // Extract booking data from Calendly event
        const eventData = {
          event_uri: e.data.payload?.event?.uri || '',
          invitee_uri: e.data.payload?.invitee?.uri || '',
          invitee_name: e.data.payload?.invitee?.name || '',
          invitee_email: e.data.payload?.invitee?.email || '',
          scheduled_at: new Date().toISOString(),
          event_type: e.data.payload?.event_type?.name || 'consultation',
          start_time: e.data.payload?.event?.start_time || '',
          end_time: e.data.payload?.event?.end_time || '',
          meeting_url: e.data.payload?.event?.location?.join_url || '',
          raw_data: e.data.payload
        }

        // Save to Supabase for free plan users (fallback method)
        try {
          await saveBookingToSupabase(eventData)
          setBookingSuccess(true)
          console.log('✅ Booking saved to database successfully!')
        } catch (error) {
          console.error('❌ Error saving booking:', error)
        }

        // Show success message
        if (!process.env.CALENDLY_WEBHOOK_SECRET) {
          console.log('📅 Booking completed! Data saved locally. Check your Calendly dashboard for details.')
        }

        // Close modal after successful booking
        setTimeout(() => {
          handleClose()
        }, 3000) // Extended time to show success message
      }
    }

    window.addEventListener('message', handleCalendlyEvent)
    return () => window.removeEventListener('message', handleCalendlyEvent)
  }, [isOpen, calendlyLoaded, handleClose])

  // Function to save booking data to Supabase
  const saveBookingToSupabase = async (bookingData: any) => {
    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Error saving booking to Supabase:', error)
      throw error
    }
  }

  if (!isOpen) return null

  return (
    <div className="calendly-modal-overlay" onClick={handleClose}>
      <div 
        className="calendly-modal-content" 
        onClick={(e) => e.stopPropagation()}
      >
        {/* Modal Header */}
        <div className="calendly-modal-header">
          <h2>Schedule a Call</h2>
          <button 
            className="calendly-close-btn"
            onClick={handleClose}
            aria-label="Close booking modal"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path 
                d="M18 6L6 18M6 6L18 18" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className="calendly-modal-body">
          {bookingSuccess && (
            <div className="calendly-success">
              <div className="success-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <path d="m9 12 2 2 4-4" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Booking Confirmed!</h3>
              <p>Your meeting has been scheduled successfully. You'll receive a confirmation email shortly.</p>
              {!process.env.CALENDLY_WEBHOOK_SECRET && (
                <p className="free-plan-note">
                  📅 Booking data has been saved locally. Check your Calendly dashboard for full details.
                </p>
              )}
            </div>
          )}

          {!bookingSuccess && isLoading && (
            <div className="calendly-loading">
              <div className="calendly-skeleton">
                <div className="skeleton-header"></div>
                <div className="skeleton-calendar"></div>
                <div className="skeleton-times"></div>
              </div>
              <p>Loading calendar...</p>
            </div>
          )}

          {!bookingSuccess && hasError && (
            <div className="calendly-error">
              <div className="error-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
                  <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
                </svg>
              </div>
              <h3>Unable to load calendar</h3>
              <p>We're having trouble loading the booking calendar. Please try again or use the direct link below.</p>
              <a
                href={calendlyUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="calendly-fallback-btn"
              >
                Open in new tab
              </a>
            </div>
          )}

          {!bookingSuccess && !isLoading && !hasError && (
            <div
              id="calendly-inline-widget"
              className="calendly-widget-container"
            />
          )}
        </div>
      </div>
    </div>
  )
}

// Extend Window interface for Calendly
declare global {
  interface Window {
    Calendly: {
      initInlineWidget: (options: {
        url: string
        parentElement: HTMLElement | null
        prefill?: Record<string, any>
        utm?: Record<string, string>
      }) => void
    }
  }
}
