# Google Calendar API Integration Setup

## 🎯 Overview

This guide shows how to set up Google Calendar API integration as an alternative to Calendly webhooks for free plan users. When <PERSON>nd<PERSON> is connected to Google Calendar, this integration can automatically sync booking data to Supabase.

## ✅ Benefits

- ✅ Works with Calendly free plan
- ✅ Automatic booking data sync
- ✅ No webhook limitations
- ✅ Real-time calendar access
- ✅ Scheduled sync capabilities

## 🚀 Setup Instructions

### Step 1: Google Cloud Console Setup

1. **Go to Google Cloud Console**
   - Visit: https://console.cloud.google.com/
   - Create a new project or select existing one

2. **Enable Google Calendar API**
   - Go to "APIs & Services" → "Library"
   - Search for "Google Calendar API"
   - Click "Enable"

3. **Create Service Account (Recommended)**
   - Go to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "Service Account"
   - Fill in service account details
   - Download the JSON key file

### Step 2: Environment Configuration

Add these variables to your `.env.local`:

```env
# Google Calendar API - Service Account (Recommended)
GOOGLE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"..."}

# OR Google Calendar API - OAuth2 (Alternative)
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
GOOGLE_REFRESH_TOKEN=your_refresh_token

# Calendar Configuration
GOOGLE_CALENDAR_ID=primary
GOOGLE_CALENDAR_OWNER_EMAIL=<EMAIL>
```

### Step 3: Calendar Permissions

**For Service Account:**
1. Copy the service account email from the JSON key
2. Go to Google Calendar settings
3. Share your calendar with the service account email
4. Give "Make changes to events" permission

**For OAuth2:**
1. Set up OAuth consent screen
2. Add your email as test user
3. Generate refresh token using OAuth playground

### Step 4: Connect Calendly to Google Calendar

1. **In Calendly Dashboard:**
   - Go to Account → Calendar Connections
   - Connect your Google Calendar
   - Ensure events are being created in Google Calendar

2. **Test the Connection:**
   - Book a test meeting through Calendly
   - Verify it appears in Google Calendar
   - Check that attendee information is included

## 🔄 Usage

### Manual Sync

Trigger manual sync via API:

```bash
curl -X POST http://localhost:3000/api/sync/google-calendar \
  -H "Content-Type: application/json" \
  -d '{"daysBack": 7}'
```

### Scheduled Sync

**Option 1: Vercel Cron (Recommended)**

Create `vercel.json`:
```json
{
  "crons": [
    {
      "path": "/api/sync/google-calendar",
      "schedule": "0 */6 * * *"
    }
  ]
}
```

**Option 2: External Cron Service**
- Use services like cron-job.org
- Set up to call your sync endpoint every few hours

### Check Sync Status

```bash
curl http://localhost:3000/api/sync/google-calendar
```

## 🛠️ Advanced Configuration

### Custom Calendar ID

If using a specific calendar (not primary):

```env
GOOGLE_CALENDAR_ID=<EMAIL>
```

### Sync Parameters

```javascript
// Sync last 14 days
POST /api/sync/google-calendar
{
  "daysBack": 14,
  "forceSync": true
}
```

### Event Filtering

The sync automatically filters for:
- Events containing "calendly" in the title
- Events with attendees (excluding calendar owner)
- Events within the specified date range

## 🔍 Troubleshooting

### Common Issues

**1. "Google Calendar API not configured"**
- Check environment variables are set correctly
- Verify JSON key format for service account

**2. "Insufficient permissions"**
- Ensure calendar is shared with service account
- Check OAuth scopes include calendar.readonly

**3. "No events found"**
- Verify Calendly is creating events in Google Calendar
- Check calendar ID is correct
- Ensure events contain "calendly" in title

**4. "Authentication failed"**
- Regenerate service account key
- Check OAuth refresh token is valid

### Debug Mode

Enable detailed logging:

```env
DEBUG=google-calendar
```

## 📊 Monitoring

### Sync Logs

Check application logs for:
- `🔄 Syncing calendar events...`
- `📅 Found X calendar events`
- `✅ Synced booking: email - event`

### Database Verification

Check Supabase bookings table:
- Look for `calendly_id` starting with `gcal-`
- Verify `raw_data` contains Google Calendar event data

## 🔮 Future Enhancements

### Planned Features

- [ ] Real-time sync via Google Calendar webhooks
- [ ] Bi-directional sync (update calendar from Supabase)
- [ ] Multiple calendar support
- [ ] Advanced event filtering
- [ ] Sync analytics dashboard

### Integration with Other Services

- Zapier integration for additional automation
- Slack notifications for new bookings
- Email marketing platform sync

## 💡 Best Practices

### Security

- Use service account for server-side access
- Limit calendar permissions to minimum required
- Rotate credentials regularly
- Monitor API usage quotas

### Performance

- Sync only recent events (7-14 days)
- Use incremental sync when possible
- Cache calendar data to reduce API calls
- Monitor rate limits

### Reliability

- Implement retry logic for failed syncs
- Log all sync operations
- Set up monitoring alerts
- Have fallback to manual sync

## 🚀 Ready to Use

Once configured, your booking system will automatically sync calendar events to Supabase, providing the same functionality as Calendly webhooks without requiring a paid plan!

### Quick Test

1. Book a test meeting through Calendly
2. Wait for event to appear in Google Calendar
3. Run manual sync: `POST /api/sync/google-calendar`
4. Check Supabase bookings table for new entry

The Google Calendar integration provides a robust alternative for free plan users while maintaining all the benefits of automated booking tracking!
