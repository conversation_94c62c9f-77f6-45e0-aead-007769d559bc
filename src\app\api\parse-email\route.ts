import { NextRequest, NextResponse } from 'next/server'
import { CalendlyEmailParser } from '@/lib/email-parser'

/**
 * Email Parsing API Route
 * 
 * This endpoint accepts email content and attempts to parse Calendly booking data
 * Useful for integrating with email forwarding services or manual email processing
 * 
 * Usage:
 * POST /api/parse-email
 * {
 *   "subject": "Event Confirmed: 30-minute consultation with <PERSON>",
 *   "content": "Your event has been confirmed...",
 *   "autoSave": true
 * }
 */

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { subject, content, autoSave = false } = body

    // Validate input
    if (!subject || !content) {
      return NextResponse.json(
        {
          error: 'Missing required fields',
          message: 'Both subject and content are required',
          required_fields: ['subject', 'content']
        },
        { status: 400 }
      )
    }

    console.log('📧 Parsing email:', { subject: subject.substring(0, 50) + '...' })

    // Parse the email
    const parseResult = CalendlyEmailParser.parseEmail(content, subject)

    if (!parseResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: parseResult.error,
          confidence: parseResult.confidence,
          suggestions: [
            'Ensure the email is a Calendly notification',
            'Check that the email contains booking details',
            'Verify the email format is not corrupted'
          ]
        },
        { status: 400 }
      )
    }

    // Auto-save if requested and confidence is high enough
    let saved = false
    if (autoSave && parseResult.confidence >= 0.7 && parseResult.data) {
      try {
        saved = await CalendlyEmailParser.saveBookingFromEmail(parseResult.data)
      } catch (error) {
        console.error('Error auto-saving booking:', error)
      }
    }

    return NextResponse.json(
      {
        success: true,
        data: parseResult.data,
        confidence: parseResult.confidence,
        auto_saved: saved,
        message: saved 
          ? 'Email parsed and booking saved successfully'
          : 'Email parsed successfully (not saved)',
        recommendations: {
          confidence_level: parseResult.confidence >= 0.8 ? 'high' : 
                           parseResult.confidence >= 0.6 ? 'medium' : 'low',
          should_review: parseResult.confidence < 0.8,
          missing_fields: this.getMissingFields(parseResult.data)
        }
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Email parsing failed:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'Email parsing failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }

  // Helper method to identify missing fields
  static getMissingFields(data: any): string[] {
    if (!data) return ['all_fields']
    
    const required = ['invitee_email', 'start_time', 'event_name']
    const optional = ['invitee_name', 'end_time', 'timezone', 'location', 'meeting_url']
    
    const missing: string[] = []
    
    required.forEach(field => {
      if (!data[field]) missing.push(field)
    })
    
    optional.forEach(field => {
      if (!data[field]) missing.push(`${field} (optional)`)
    })
    
    return missing
  }
}

// Handle GET requests for documentation
export async function GET() {
  return NextResponse.json(
    {
      endpoint: '/api/parse-email',
      description: 'Parse Calendly email notifications to extract booking data',
      methods: ['POST'],
      request_format: {
        subject: 'Email subject line',
        content: 'Email body content (HTML or plain text)',
        autoSave: 'Boolean - automatically save if confidence is high (optional)'
      },
      response_format: {
        success: 'Boolean indicating if parsing was successful',
        data: 'Parsed booking data object (if successful)',
        confidence: 'Number 0-1 indicating parsing confidence',
        auto_saved: 'Boolean indicating if booking was automatically saved',
        recommendations: 'Object with confidence level and review suggestions'
      },
      supported_email_types: [
        'Calendly booking confirmations',
        'Calendly booking cancellations',
        'Calendly booking reschedules'
      ],
      integration_examples: {
        zapier: 'Use Zapier Email Parser to forward Calendly emails to this endpoint',
        manual: 'Copy/paste email content for manual processing',
        email_forwarding: 'Set up email forwarding rules to send Calendly emails here'
      },
      confidence_levels: {
        high: '0.8+ - Safe to auto-save',
        medium: '0.6-0.8 - Review recommended',
        low: '0.0-0.6 - Manual verification required'
      }
    },
    { status: 200 }
  )
}
