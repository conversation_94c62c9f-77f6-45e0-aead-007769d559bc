'use client'

import { useState, useEffect } from 'react'
import { Booking } from '@/lib/supabase'

interface BookingFormData {
  invitee_name: string
  invitee_email: string
  event_type: string
  scheduled_at: string
  start_time: string
  end_time: string
  location: string
  notes: string
}

export default function AdminBookingsPage() {
  const [bookings, setBookings] = useState<Booking[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [showEmailParser, setShowEmailParser] = useState(false)
  const [emailData, setEmailData] = useState({ subject: '', content: '' })
  const [formData, setFormData] = useState<BookingFormData>({
    invitee_name: '',
    invitee_email: '',
    event_type: 'consultation',
    scheduled_at: '',
    start_time: '',
    end_time: '',
    location: 'Online',
    notes: ''
  })

  useEffect(() => {
    fetchBookings()
  }, [])

  const fetchBookings = async () => {
    try {
      const response = await fetch('/api/bookings')
      if (response.ok) {
        const data = await response.json()
        setBookings(data.bookings || [])
      }
    } catch (error) {
      console.error('Error fetching bookings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const bookingData = {
        ...formData,
        calendly_id: `manual-${Date.now()}`,
        status: 'active',
        raw_data: { source: 'manual_entry', created_by: 'admin' }
      }

      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(bookingData)
      })

      if (response.ok) {
        alert('Booking added successfully!')
        setShowForm(false)
        setFormData({
          invitee_name: '',
          invitee_email: '',
          event_type: 'consultation',
          scheduled_at: '',
          start_time: '',
          end_time: '',
          location: 'Online',
          notes: ''
        })
        fetchBookings()
      } else {
        alert('Failed to add booking')
      }
    } catch (error) {
      console.error('Error adding booking:', error)
      alert('Error adding booking')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const syncGoogleCalendar = async () => {
    try {
      const response = await fetch('/api/sync/google-calendar', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ daysBack: 14 })
      })

      if (response.ok) {
        alert('Google Calendar sync completed!')
        fetchBookings()
      } else {
        const error = await response.json()
        alert(`Sync failed: ${error.message}`)
      }
    } catch (error) {
      console.error('Error syncing calendar:', error)
      alert('Error syncing calendar')
    }
  }

  const parseEmail = async () => {
    try {
      const response = await fetch('/api/parse-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subject: emailData.subject,
          content: emailData.content,
          autoSave: true
        })
      })

      const result = await response.json()

      if (result.success) {
        alert(`Email parsed successfully! Confidence: ${(result.confidence * 100).toFixed(0)}%${result.auto_saved ? ' (Booking saved)' : ''}`)
        if (result.auto_saved) {
          fetchBookings()
        }
        setShowEmailParser(false)
        setEmailData({ subject: '', content: '' })
      } else {
        alert(`Parsing failed: ${result.message}`)
      }
    } catch (error) {
      console.error('Error parsing email:', error)
      alert('Error parsing email')
    }
  }

  if (loading) {
    return (
      <div className="admin-container">
        <div className="loading">Loading bookings...</div>
      </div>
    )
  }

  return (
    <div className="admin-container">
      <div className="admin-header">
        <h1>Booking Management</h1>
        <div className="admin-actions">
          <button onClick={syncGoogleCalendar} className="btn btn-secondary">
            Sync Google Calendar
          </button>
          <button onClick={() => setShowEmailParser(true)} className="btn btn-secondary">
            Parse Email
          </button>
          <button onClick={() => setShowForm(true)} className="btn btn-primary">
            Add Manual Booking
          </button>
        </div>
      </div>

      {showEmailParser && (
        <div className="booking-form-overlay">
          <div className="booking-form-container">
            <div className="form-header">
              <h2>Parse Calendly Email</h2>
              <button onClick={() => setShowEmailParser(false)} className="close-btn">×</button>
            </div>

            <div className="email-parser-form" style={{ padding: '24px' }}>
              <p style={{ color: 'rgba(255, 255, 255, 0.8)', marginBottom: '20px' }}>
                Copy and paste a Calendly email notification to automatically extract booking data.
              </p>

              <div className="form-group">
                <label htmlFor="email_subject">Email Subject</label>
                <input
                  type="text"
                  id="email_subject"
                  value={emailData.subject}
                  onChange={(e) => setEmailData(prev => ({ ...prev, subject: e.target.value }))}
                  placeholder="e.g., Event Confirmed: 30-minute consultation with John Doe"
                />
              </div>

              <div className="form-group">
                <label htmlFor="email_content">Email Content</label>
                <textarea
                  id="email_content"
                  value={emailData.content}
                  onChange={(e) => setEmailData(prev => ({ ...prev, content: e.target.value }))}
                  rows={10}
                  placeholder="Paste the full email content here..."
                />
              </div>

              <div className="form-actions">
                <button
                  type="button"
                  onClick={() => setShowEmailParser(false)}
                  className="btn btn-secondary"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={parseEmail}
                  className="btn btn-primary"
                  disabled={!emailData.subject || !emailData.content}
                >
                  Parse Email
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showForm && (
        <div className="booking-form-overlay">
          <div className="booking-form-container">
            <div className="form-header">
              <h2>Add Manual Booking</h2>
              <button onClick={() => setShowForm(false)} className="close-btn">×</button>
            </div>

            <form onSubmit={handleSubmit} className="booking-form">
              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="invitee_name">Client Name *</label>
                  <input
                    type="text"
                    id="invitee_name"
                    name="invitee_name"
                    value={formData.invitee_name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="invitee_email">Client Email *</label>
                  <input
                    type="email"
                    id="invitee_email"
                    name="invitee_email"
                    value={formData.invitee_email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="event_type">Event Type</label>
                  <select
                    id="event_type"
                    name="event_type"
                    value={formData.event_type}
                    onChange={handleInputChange}
                  >
                    <option value="consultation">Consultation</option>
                    <option value="discovery">Discovery Call</option>
                    <option value="strategy">Strategy Session</option>
                    <option value="demo">Demo</option>
                    <option value="follow-up">Follow-up</option>
                  </select>
                </div>
                <div className="form-group">
                  <label htmlFor="location">Location</label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="start_time">Start Time *</label>
                  <input
                    type="datetime-local"
                    id="start_time"
                    name="start_time"
                    value={formData.start_time}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="end_time">End Time *</label>
                  <input
                    type="datetime-local"
                    id="end_time"
                    name="end_time"
                    value={formData.end_time}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label htmlFor="notes">Notes</label>
                <textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Additional notes about the booking..."
                />
              </div>

              <div className="form-actions">
                <button type="button" onClick={() => setShowForm(false)} className="btn btn-secondary">
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  Add Booking
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="bookings-list">
        <div className="list-header">
          <h2>Recent Bookings ({bookings.length})</h2>
        </div>

        {bookings.length === 0 ? (
          <div className="empty-state">
            <p>No bookings found. Add your first booking manually or sync from Google Calendar.</p>
          </div>
        ) : (
          <div className="bookings-table">
            <div className="table-header">
              <div>Client</div>
              <div>Event Type</div>
              <div>Date & Time</div>
              <div>Status</div>
              <div>Source</div>
            </div>
            {bookings.map((booking) => (
              <div key={booking.id} className="table-row">
                <div className="client-info">
                  <div className="name">{booking.invitee_name || 'Unknown'}</div>
                  <div className="email">{booking.invitee_email}</div>
                </div>
                <div className="event-type">{booking.event_type}</div>
                <div className="datetime">
                  {new Date(booking.start_time).toLocaleDateString()}<br />
                  <small>{new Date(booking.start_time).toLocaleTimeString()}</small>
                </div>
                <div className={`status status-${booking.status}`}>
                  {booking.status}
                </div>
                <div className="source">
                  {booking.calendly_id.startsWith('manual-') ? 'Manual' : 
                   booking.calendly_id.startsWith('gcal-') ? 'Google Calendar' : 'Calendly'}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
