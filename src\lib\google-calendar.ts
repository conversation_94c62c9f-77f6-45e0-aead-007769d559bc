/**
 * Google Calendar API Integration
 * Alternative booking sync method for Calendly free plan users
 * 
 * Setup Instructions:
 * 1. Go to Google Cloud Console
 * 2. Enable Google Calendar API
 * 3. Create credentials (Service Account or OAuth2)
 * 4. Add credentials to environment variables
 */

import { google } from 'googleapis'
import { db } from './supabase'

// Google Calendar API configuration
const SCOPES = ['https://www.googleapis.com/auth/calendar.readonly']

interface CalendarEvent {
  id: string
  summary: string
  description?: string
  start: {
    dateTime: string
    timeZone?: string
  }
  end: {
    dateTime: string
    timeZone?: string
  }
  attendees?: Array<{
    email: string
    displayName?: string
    responseStatus: string
  }>
  location?: string
  htmlLink?: string
  created: string
  updated: string
}

interface BookingData {
  calendly_id: string
  event_type: string
  event_name: string
  invitee_email: string
  invitee_name: string
  invitee_timezone: string
  scheduled_at: string
  start_time: string
  end_time: string
  status: 'active' | 'canceled' | 'no_show'
  meeting_url?: string
  location?: string
  raw_data: any
}

class GoogleCalendarSync {
  private calendar: any
  private calendarId: string

  constructor() {
    this.calendarId = process.env.GOOGLE_CALENDAR_ID || 'primary'
    this.initializeAuth()
  }

  private initializeAuth() {
    try {
      // Service Account authentication (recommended for server-side)
      if (process.env.GOOGLE_SERVICE_ACCOUNT_KEY) {
        const serviceAccount = JSON.parse(process.env.GOOGLE_SERVICE_ACCOUNT_KEY)
        const auth = new google.auth.GoogleAuth({
          credentials: serviceAccount,
          scopes: SCOPES,
        })
        this.calendar = google.calendar({ version: 'v3', auth })
      }
      // OAuth2 authentication (for user-based access)
      else if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
        const oauth2Client = new google.auth.OAuth2(
          process.env.GOOGLE_CLIENT_ID,
          process.env.GOOGLE_CLIENT_SECRET,
          process.env.GOOGLE_REDIRECT_URI
        )
        
        if (process.env.GOOGLE_REFRESH_TOKEN) {
          oauth2Client.setCredentials({
            refresh_token: process.env.GOOGLE_REFRESH_TOKEN
          })
        }
        
        this.calendar = google.calendar({ version: 'v3', auth: oauth2Client })
      } else {
        console.warn('⚠️ Google Calendar API credentials not configured')
      }
    } catch (error) {
      console.error('❌ Failed to initialize Google Calendar auth:', error)
    }
  }

  /**
   * Fetch recent calendar events and sync to Supabase
   */
  async syncRecentBookings(daysBack: number = 7): Promise<void> {
    if (!this.calendar) {
      throw new Error('Google Calendar API not initialized')
    }

    try {
      const timeMin = new Date()
      timeMin.setDate(timeMin.getDate() - daysBack)
      
      const timeMax = new Date()
      timeMax.setDate(timeMax.getDate() + 30) // Next 30 days

      console.log(`🔄 Syncing calendar events from ${timeMin.toISOString()} to ${timeMax.toISOString()}`)

      const response = await this.calendar.events.list({
        calendarId: this.calendarId,
        timeMin: timeMin.toISOString(),
        timeMax: timeMax.toISOString(),
        singleEvents: true,
        orderBy: 'startTime',
        q: 'calendly', // Filter for Calendly events
      })

      const events: CalendarEvent[] = response.data.items || []
      console.log(`📅 Found ${events.length} calendar events`)

      for (const event of events) {
        await this.processCalendarEvent(event)
      }

      console.log('✅ Calendar sync completed successfully')
    } catch (error) {
      console.error('❌ Error syncing calendar events:', error)
      throw error
    }
  }

  /**
   * Process individual calendar event and save to Supabase
   */
  private async processCalendarEvent(event: CalendarEvent): Promise<void> {
    try {
      // Skip events without attendees (likely not bookings)
      if (!event.attendees || event.attendees.length === 0) {
        return
      }

      // Find the client attendee (not the calendar owner)
      const clientAttendee = event.attendees.find(
        attendee => attendee.email !== process.env.GOOGLE_CALENDAR_OWNER_EMAIL
      )

      if (!clientAttendee) {
        return
      }

      // Create booking data from calendar event
      const bookingData: BookingData = {
        calendly_id: `gcal-${event.id}`,
        event_type: this.extractEventType(event.summary || ''),
        event_name: event.summary || 'Calendar Meeting',
        invitee_email: clientAttendee.email,
        invitee_name: clientAttendee.displayName || '',
        invitee_timezone: event.start.timeZone || 'UTC',
        scheduled_at: event.created,
        start_time: event.start.dateTime,
        end_time: event.end.dateTime,
        status: this.mapEventStatus(clientAttendee.responseStatus),
        meeting_url: event.htmlLink,
        location: event.location || 'Online',
        raw_data: event
      }

      // Save to Supabase
      await db.bookings.upsert(bookingData)
      console.log(`✅ Synced booking: ${bookingData.invitee_email} - ${bookingData.event_name}`)

    } catch (error) {
      console.error('❌ Error processing calendar event:', error)
    }
  }

  /**
   * Extract event type from calendar event summary
   */
  private extractEventType(summary: string): string {
    const lowerSummary = summary.toLowerCase()
    
    if (lowerSummary.includes('consultation')) return 'consultation'
    if (lowerSummary.includes('discovery')) return 'discovery'
    if (lowerSummary.includes('strategy')) return 'strategy'
    if (lowerSummary.includes('demo')) return 'demo'
    if (lowerSummary.includes('30')) return '30-minute-call'
    if (lowerSummary.includes('60')) return '60-minute-call'
    
    return 'meeting'
  }

  /**
   * Map Google Calendar response status to booking status
   */
  private mapEventStatus(responseStatus: string): 'active' | 'canceled' | 'no_show' {
    switch (responseStatus) {
      case 'accepted':
        return 'active'
      case 'declined':
        return 'canceled'
      case 'tentative':
        return 'active'
      default:
        return 'active'
    }
  }

  /**
   * Check if Google Calendar API is configured
   */
  static isConfigured(): boolean {
    return !!(
      process.env.GOOGLE_SERVICE_ACCOUNT_KEY ||
      (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET)
    )
  }
}

export { GoogleCalendarSync }
export default GoogleCalendarSync
