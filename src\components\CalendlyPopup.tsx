'use client'

import { useEffect } from 'react'
import Script from 'next/script'

interface CalendlyPopupProps {
  url?: string
  buttonText?: string
  buttonClassName?: string
  prefill?: {
    name?: string
    email?: string
    customAnswers?: Record<string, string>
  }
  utm?: {
    utmCampaign?: string
    utmSource?: string
    utmMedium?: string
    utmContent?: string
    utmTerm?: string
  }
}

const CalendlyPopup = ({ 
  url = process.env.NEXT_PUBLIC_CALENDLY_URL || "https://calendly.com/your-scheduling-link",
  buttonText = "Book a Meeting",
  buttonClassName = "btn btn-secondary",
  prefill = {},
  utm = {
    utmCampaign: 'portfolio-website',
    utmSource: 'website',
    utmMedium: 'popup-widget'
  }
}: CalendlyPopupProps) => {
  
  useEffect(() => {
    // Listen for Calendly events
    const handleCalendlyEvent = async (e: MessageEvent) => {
      if (e.data.event && e.data.event.indexOf('calendly') === 0) {
        console.log('Calendly Event:', e.data)
        
        // Handle different Calendly events
        if (e.data.event === 'calendly.event_scheduled') {
          // Event was successfully scheduled
          const eventData = {
            event_uri: e.data.payload?.event?.uri || '',
            invitee_uri: e.data.payload?.invitee?.uri || '',
            invitee_name: e.data.payload?.invitee?.name || '',
            invitee_email: e.data.payload?.invitee?.email || '',
            scheduled_at: new Date().toISOString(),
            event_type: e.data.payload?.event_type?.name || 'consultation',
            start_time: e.data.payload?.event?.start_time || '',
            end_time: e.data.payload?.event?.end_time || '',
            meeting_url: e.data.payload?.event?.location?.join_url || '',
            location: e.data.payload?.event?.location?.location || 'Online',
            raw_data: e.data.payload
          }
          
          // Send to API to store in Supabase
          await saveBookingToSupabase(eventData)
        }
      }
    }

    window.addEventListener('message', handleCalendlyEvent)
    
    return () => {
      window.removeEventListener('message', handleCalendlyEvent)
    }
  }, [])

  const saveBookingToSupabase = async (bookingData: any) => {
    try {
      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bookingData),
      })
      
      if (response.ok) {
        console.log('✅ Booking saved to Supabase successfully!')
        
        // Show success notification
        if ('Notification' in window && Notification.permission === 'granted') {
          new Notification('Booking Confirmed!', {
            body: 'Your meeting has been scheduled successfully.',
            icon: '/favicon.ico'
          })
        }
      } else {
        console.error('❌ Failed to save booking:', response.statusText)
      }
    } catch (error) {
      console.error('❌ Error saving booking:', error)
    }
  }

  const openCalendlyPopup = () => {
    if (window.Calendly) {
      window.Calendly.initPopupWidget({
        url: url,
        prefill: prefill,
        utm: utm,
      })
    } else {
      console.warn('Calendly script not loaded yet')
      // Fallback: open in new tab
      window.open(url, '_blank', 'noopener,noreferrer')
    }
  }

  return (
    <>
      <Script
        src="https://assets.calendly.com/assets/external/widget.js"
        strategy="lazyOnload"
        onLoad={() => {
          console.log('✅ Calendly script loaded successfully')
        }}
        onError={() => {
          console.error('❌ Failed to load Calendly script')
        }}
      />
      
      <button
        onClick={openCalendlyPopup}
        className={buttonClassName}
        type="button"
      >
        {buttonText}
      </button>
    </>
  )
}

export default CalendlyPopup

// Extend Window interface for Calendly
declare global {
  interface Window {
    Calendly: {
      initPopupWidget: (options: {
        url: string
        prefill?: Record<string, any>
        utm?: Record<string, string>
      }) => void
      initInlineWidget: (options: {
        url: string
        parentElement: HTMLElement | null
        prefill?: Record<string, any>
        utm?: Record<string, string>
      }) => void
    }
  }
}
