import { NextRequest, NextResponse } from 'next/server'
import { GoogleCalendarSync } from '@/lib/google-calendar'

/**
 * Google Calendar Sync API Route
 * 
 * This endpoint allows manual or scheduled syncing of calendar events
 * from Google Calendar to Supabase as an alternative to Calendly webhooks
 * 
 * Usage:
 * - Manual sync: POST /api/sync/google-calendar
 * - Scheduled sync: Set up cron job or Vercel cron to call this endpoint
 */

export async function POST(request: NextRequest) {
  try {
    // Check if Google Calendar is configured
    if (!GoogleCalendarSync.isConfigured()) {
      return NextResponse.json(
        {
          error: 'Google Calendar API not configured',
          message: 'Please set up Google Calendar API credentials in environment variables',
          setup_guide: {
            required_env_vars: [
              'GOOGLE_SERVICE_ACCOUNT_KEY (recommended)',
              'OR GOOGLE_CLIENT_ID + GOOGLE_CLIENT_SECRET + GOOGLE_REFRESH_TOKEN',
              'GOOGLE_CALENDAR_ID (optional, defaults to primary)',
              'GOO<PERSON><PERSON>_CALENDAR_OWNER_EMAIL (to filter out owner from attendees)'
            ],
            documentation: 'See docs/GOOGLE_CALENDAR_SETUP.md for detailed setup instructions'
          }
        },
        { status: 400 }
      )
    }

    // Parse request parameters
    const body = await request.json().catch(() => ({}))
    const daysBack = body.daysBack || 7
    const forceSync = body.forceSync || false

    console.log(`🔄 Starting Google Calendar sync (${daysBack} days back, force: ${forceSync})`)

    // Initialize Google Calendar sync
    const calendarSync = new GoogleCalendarSync()

    // Perform sync
    await calendarSync.syncRecentBookings(daysBack)

    return NextResponse.json(
      {
        success: true,
        message: 'Google Calendar sync completed successfully',
        synced_period: `${daysBack} days back`,
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Google Calendar sync failed:', error)

    return NextResponse.json(
      {
        error: 'Google Calendar sync failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// Handle GET requests for sync status
export async function GET(request: NextRequest) {
  try {
    const isConfigured = GoogleCalendarSync.isConfigured()
    
    return NextResponse.json(
      {
        google_calendar_configured: isConfigured,
        sync_available: isConfigured,
        last_sync: null, // TODO: Store last sync timestamp in database
        setup_status: {
          service_account: !!process.env.GOOGLE_SERVICE_ACCOUNT_KEY,
          oauth_credentials: !!(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET),
          refresh_token: !!process.env.GOOGLE_REFRESH_TOKEN,
          calendar_id: !!process.env.GOOGLE_CALENDAR_ID,
          owner_email: !!process.env.GOOGLE_CALENDAR_OWNER_EMAIL
        },
        instructions: {
          manual_sync: 'POST /api/sync/google-calendar',
          scheduled_sync: 'Set up Vercel cron or external scheduler',
          documentation: 'docs/GOOGLE_CALENDAR_SETUP.md'
        }
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('❌ Error checking Google Calendar status:', error)

    return NextResponse.json(
      {
        error: 'Failed to check Google Calendar status',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
