# Email Parsing System for Calendly

## 🎯 Overview

The email parsing system allows you to automatically extract booking data from Calendly email notifications. This is perfect for free plan users who don't have access to webhooks but still want to track bookings in Supabase.

## ✅ Benefits

- ✅ Works with Calendly free plan
- ✅ No webhook limitations
- ✅ Automatic data extraction
- ✅ Manual and automated processing
- ✅ High accuracy parsing

## 🚀 How It Works

1. **Calendly sends email notifications** for bookings
2. **Forward emails** to your parsing system
3. **Extract booking data** automatically
4. **Save to Supabase** for tracking and analytics

## 📧 Supported Email Types

- ✅ **Booking Confirmations** - "Event Confirmed: ..."
- ✅ **Booking Cancellations** - "Event Canceled: ..."
- ✅ **Booking Reschedules** - "Event Rescheduled: ..."

## 🛠️ Setup Methods

### Method 1: Manual Processing (Immediate)

1. **Access Admin Panel**
   - Go to `/admin/bookings`
   - Click "Parse Email" button

2. **Copy Email Content**
   - Open Calendly email notification
   - Copy subject line and full email content
   - Paste into the parsing form

3. **Parse and Save**
   - Click "Parse Email"
   - Review extracted data
   - Booking is automatically saved if confidence is high

### Method 2: Email Forwarding (Automated)

1. **Set up Email Forwarding**
   - In your email client, create a rule
   - Forward emails from `<EMAIL>` to your system
   - Use a service like Zapier Email Parser

2. **Configure Zapier (Recommended)**
   ```
   Trigger: Email Parser by Zapier
   - Set up mailbox: <EMAIL>
   - Forward Calendly emails to this address
   
   Action: Webhooks by Zapier
   - URL: https://yourdomain.com/api/parse-email
   - Method: POST
   - Data: {"subject": "{{subject}}", "content": "{{body_plain}}", "autoSave": true}
   ```

3. **Alternative: Direct Email Integration**
   - Use services like SendGrid Inbound Parse
   - Configure webhook to your parsing endpoint
   - Process emails automatically

### Method 3: API Integration

Direct API usage for custom integrations:

```bash
curl -X POST https://yourdomain.com/api/parse-email \
  -H "Content-Type: application/json" \
  -d '{
    "subject": "Event Confirmed: 30-minute consultation with John Doe",
    "content": "Your event has been confirmed...",
    "autoSave": true
  }'
```

## 📊 Parsing Accuracy

The system provides confidence scores for parsed data:

- **High (80-100%)** - Auto-saved, very reliable
- **Medium (60-79%)** - Review recommended
- **Low (0-59%)** - Manual verification required

### What Gets Extracted

- ✅ **Client Name** - From email content
- ✅ **Client Email** - Email address
- ✅ **Event Type** - Meeting type/duration
- ✅ **Date & Time** - Start and end times
- ✅ **Timezone** - Meeting timezone
- ✅ **Location** - Meeting location/URL
- ✅ **Meeting URL** - Zoom/Meet links

## 🔧 Configuration

### Environment Variables

No additional environment variables needed - uses existing Supabase configuration.

### Email Templates

The parser works with standard Calendly email templates. For best results:

1. **Don't modify** Calendly email templates
2. **Forward complete emails** (not excerpts)
3. **Include HTML content** when possible

## 🎯 Best Practices

### Email Management

1. **Create Email Rules**
   - Auto-forward Calendly emails
   - Keep originals in a dedicated folder
   - Set up backup forwarding

2. **Regular Processing**
   - Check parsing results weekly
   - Review low-confidence extractions
   - Update client information as needed

3. **Quality Control**
   - Verify extracted data accuracy
   - Cross-reference with Calendly dashboard
   - Update any missing information

### Automation Tips

1. **Zapier Integration**
   - Use Zapier's Email Parser for reliability
   - Set up error notifications
   - Monitor parsing success rates

2. **Backup Methods**
   - Keep manual parsing as backup
   - Export Calendly data monthly
   - Maintain email archives

## 🔍 Troubleshooting

### Common Issues

**1. "Email does not appear to be a Calendly notification"**
- Verify email is from Calendly
- Check subject line contains booking info
- Ensure email content is complete

**2. "Could not extract booking data"**
- Email might be corrupted or incomplete
- Try copying raw email source
- Check for missing date/time information

**3. "Low confidence score"**
- Review extracted data manually
- Update parsing rules if needed
- Use manual booking entry as fallback

### Debug Steps

1. **Check Email Format**
   - Verify it's a standard Calendly email
   - Look for required fields (name, email, date)
   - Ensure content isn't truncated

2. **Test with Sample Email**
   - Use a known good Calendly email
   - Compare parsing results
   - Identify format differences

3. **Manual Verification**
   - Cross-check with Calendly dashboard
   - Verify client details
   - Confirm meeting times

## 📈 Monitoring

### Success Metrics

- **Parsing Success Rate** - % of emails parsed successfully
- **Confidence Scores** - Average confidence levels
- **Auto-Save Rate** - % of bookings auto-saved

### Regular Checks

- Weekly review of parsed bookings
- Monthly accuracy assessment
- Quarterly system optimization

## 🔮 Advanced Features

### Custom Parsing Rules

For specialized email formats, you can extend the parser:

```typescript
// Custom parsing patterns
const customPatterns = {
  clientName: /Customer:\s*([^\n]+)/i,
  eventType: /Service:\s*([^\n]+)/i,
  // Add more patterns as needed
}
```

### Integration with Other Tools

- **CRM Integration** - Sync to customer management systems
- **Analytics** - Track booking trends and patterns
- **Notifications** - Alert on new bookings or cancellations

## 🚀 Getting Started

1. **Test Manual Parsing**
   - Go to `/admin/bookings`
   - Try parsing a Calendly email
   - Verify extracted data

2. **Set Up Email Forwarding**
   - Choose your preferred method
   - Configure forwarding rules
   - Test with a sample booking

3. **Monitor and Optimize**
   - Check parsing accuracy
   - Adjust settings as needed
   - Scale up automation

The email parsing system provides a robust alternative to webhooks, ensuring you never miss a booking even on Calendly's free plan!
